.app {
  display: flex;
  height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.editor-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 2px solid #e0e0e0;
  background-color: #f8f9fa;
}

.file-tabs {
  display: flex;
  background-color: #e9ecef;
  border-bottom: 1px solid #dee2e6;
  padding: 0;
  margin: 0;
  overflow-x: auto;
}

.file-tab {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-bottom: none;
  cursor: pointer;
  white-space: nowrap;
  min-width: 120px;
  position: relative;
}

.file-tab:hover {
  background-color: #e9ecef;
}

.file-tab.active {
  background-color: white;
  border-bottom: 1px solid white;
  z-index: 1;
}

.file-tab span {
  flex: 1;
  font-size: 14px;
}

.delete-btn {
  margin-left: 8px;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
}

.delete-btn:hover {
  background-color: #dc3545;
  color: white;
}

.new-file-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
}

.new-file-btn:hover {
  background-color: #218838;
}

.editor {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor textarea {
  flex: 1;
  border: none;
  outline: none;
  padding: 16px;
  font-family: 'Courier New', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  background-color: white;
  color: #333;
}

.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.preview-header {
  background-color: #343a40;
  color: white;
  padding: 12px 16px;
  margin: 0;
}

.preview-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.preview-iframe {
  flex: 1;
  border: none;
  background-color: white;
}

/* Scrollbar styling for webkit browsers */
.editor textarea::-webkit-scrollbar {
  width: 8px;
}

.editor textarea::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.editor textarea::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.editor textarea::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 768px) {
  .app {
    flex-direction: column;
  }

  .editor-panel {
    border-right: none;
    border-bottom: 2px solid #e0e0e0;
  }

  .file-tabs {
    flex-wrap: wrap;
  }
}
