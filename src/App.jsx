import { useState, useEffect, useRef } from 'react'
import './App.css'

function App() {
  const [files, setFiles] = useState({
    'index.html': `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Project</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <h1>Hello World!</h1>
    <p>Welcome to your HTML, CSS, and JavaScript playground!</p>
    <button id="myButton">Click me!</button>
    <script src="script.js"></script>
</body>
</html>`,
    'style.css': `body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f0f0f0;
}

h1 {
    color: #333;
    text-align: center;
}

p {
    color: #666;
    text-align: center;
    font-size: 18px;
}

button {
    display: block;
    margin: 20px auto;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #0056b3;
}`,
    'script.js': `document.addEventListener('DOMContentLoaded', function() {
    const button = document.getElementById('myButton');

    button.addEventListener('click', function() {
        alert('Hello from JavaScript!');

        // Change the button text
        this.textContent = 'Clicked!';

        // Change background color
        document.body.style.backgroundColor = '#e8f4fd';
    });

    console.log('JavaScript loaded successfully!');
});`
  })

  const [activeFile, setActiveFile] = useState('index.html')
  const [previewContent, setPreviewContent] = useState('')
  const iframeRef = useRef(null)

  // Update preview when files change
  useEffect(() => {
    updatePreview()
  }, [files])

  const updatePreview = () => {
    const htmlContent = files['index.html']
    const cssContent = files['style.css']
    const jsContent = files['script.js']

    // Create a complete HTML document with embedded CSS and JS
    const fullContent = htmlContent
      .replace('<link rel="stylesheet" href="style.css">', `<style>${cssContent}</style>`)
      .replace('<script src="script.js"></script>', `<script>${jsContent}</script>`)

    setPreviewContent(fullContent)
  }

  const handleFileChange = (content) => {
    setFiles(prev => ({
      ...prev,
      [activeFile]: content
    }))
  }

  const createNewFile = () => {
    const fileName = prompt('Enter file name (with extension):')
    if (fileName && !files[fileName]) {
      setFiles(prev => ({
        ...prev,
        [fileName]: ''
      }))
      setActiveFile(fileName)
    }
  }

  const deleteFile = (fileName) => {
    if (Object.keys(files).length > 1) {
      const newFiles = { ...files }
      delete newFiles[fileName]
      setFiles(newFiles)

      if (activeFile === fileName) {
        setActiveFile(Object.keys(newFiles)[0])
      }
    }
  }

  return (
    <div className="app">
      <div className="editor-panel">
        <div className="file-tabs">
          {Object.keys(files).map(fileName => (
            <div
              key={fileName}
              className={`file-tab ${activeFile === fileName ? 'active' : ''}`}
              onClick={() => setActiveFile(fileName)}
            >
              <span>{fileName}</span>
              {Object.keys(files).length > 1 && (
                <button
                  className="delete-btn"
                  onClick={(e) => {
                    e.stopPropagation()
                    deleteFile(fileName)
                  }}
                >
                  ×
                </button>
              )}
            </div>
          ))}
          <button className="new-file-btn" onClick={createNewFile}>+</button>
        </div>

        <div className="editor">
          <textarea
            value={files[activeFile]}
            onChange={(e) => handleFileChange(e.target.value)}
            placeholder={`Edit ${activeFile}...`}
            spellCheck={false}
          />
        </div>
      </div>

      <div className="preview-panel">
        <div className="preview-header">
          <h3>Live Preview</h3>
        </div>
        <iframe
          ref={iframeRef}
          srcDoc={previewContent}
          title="Preview"
          className="preview-iframe"
        />
      </div>
    </div>
  )
}

export default App
