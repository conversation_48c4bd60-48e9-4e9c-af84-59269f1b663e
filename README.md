# HTML, CSS, JavaScript Code Editor

A web-based code editor for studying and experimenting with HTML, CSS, and JavaScript. Features a split-screen layout with a code editor on the left and live preview on the right.

## Features

### 🎨 **Split-Screen Interface**
- **Left Panel**: Code editor with file tabs
- **Right Panel**: Live preview that updates in real-time
- **Responsive Design**: Works on desktop and mobile devices

### 📁 **File Management**
- Create new files with custom names and extensions
- Switch between multiple files using tabs
- Delete files (with protection against deleting the last file)
- Auto-save functionality every 30 seconds

### ✨ **Code Editor Features**
- **Syntax-aware editing** for HTML, CSS, and JavaScript
- **Tab indentation** support (4 spaces)
- **Line and character count** display
- **Keyboard shortcuts**:
  - `Tab`: Insert 4 spaces for indentation
  - `Ctrl+S`: Update preview
  - `Ctrl+/`: Toggle comments (HTML: `<!-- -->`, CSS: `/* */`, JS: `//`)

### 🎯 **Code Formatting**
- **Auto-format** HTML, CSS, and JavaScript code
- **Smart indentation** based on file type
- **Comment toggling** with appropriate syntax for each language

### 🌙 **Theme Support**
- **Light and Dark themes** with toggle button
- **Theme persistence** - remembers your preference
- **Optimized colors** for better code readability

### 💾 **Project Management**
- **Save projects** with custom names to localStorage
- **Load saved projects** by name
- **Auto-save recovery** - automatically saves your work and offers to restore recent changes
- **Project persistence** across browser sessions

### ⚡ **Live Preview**
- **Real-time updates** as you type
- **Embedded CSS and JavaScript** - automatically injects your CSS and JS into the HTML
- **Error-free rendering** with iframe isolation
- **Manual refresh** option with Run button

## Getting Started

1. **Open the Editor**: Simply open `code-editor.html` in your web browser
2. **Start Coding**: The editor comes with sample HTML, CSS, and JavaScript files
3. **See Results**: Your changes appear instantly in the preview panel
4. **Experiment**: Try modifying the code to see how it affects the output

## Sample Project

The editor starts with a simple "Hello World" project that demonstrates:
- Basic HTML structure
- CSS styling with hover effects
- JavaScript event handling and DOM manipulation
- Interactive button that changes the page when clicked

## File Types Supported

- **HTML** (`.html`) - Structure and content
- **CSS** (`.css`) - Styling and layout
- **JavaScript** (`.js`) - Interactivity and behavior
- **Any text-based files** - Custom extensions supported

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Tab` | Insert 4-space indentation |
| `Ctrl+S` | Update preview (manual refresh) |
| `Ctrl+/` | Toggle comments for current line(s) |

## Browser Compatibility

Works in all modern browsers including:
- Chrome/Chromium
- Firefox
- Safari
- Edge

## Technical Details

- **Pure HTML, CSS, and JavaScript** - No external dependencies
- **localStorage** for project and theme persistence
- **iframe** for safe preview rendering
- **Responsive CSS Grid/Flexbox** layout
- **Auto-save** every 30 seconds

## Tips for Learning

1. **Start Simple**: Begin with basic HTML structure
2. **Experiment**: Try changing colors, fonts, and layouts in CSS
3. **Add Interactivity**: Use JavaScript to respond to user actions
4. **Use the Console**: Open browser dev tools to see JavaScript console output
5. **Save Your Work**: Use the save feature to keep interesting projects

## Future Enhancements

Potential features for future versions:
- Syntax highlighting with color coding
- Code completion and suggestions
- Error detection and highlighting
- File upload/download functionality
- Collaborative editing
- More advanced formatting options
- Plugin system for extensions

---

**Happy Coding!** 🚀

This editor is perfect for learning web development, prototyping ideas, or just experimenting with HTML, CSS, and JavaScript in a clean, distraction-free environment.
