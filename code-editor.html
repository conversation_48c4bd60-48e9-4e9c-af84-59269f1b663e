<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML, CSS, JS Code Editor</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .app {
            display: flex;
            height: 100vh;
        }

        .editor-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            border-right: 2px solid #e0e0e0;
            background-color: #f8f9fa;
        }

        .file-tabs {
            display: flex;
            background-color: #e9ecef;
            border-bottom: 1px solid #dee2e6;
            padding: 0;
            margin: 0;
            overflow-x: auto;
        }

        .file-tab {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-bottom: none;
            cursor: pointer;
            white-space: nowrap;
            min-width: 120px;
            position: relative;
        }

        .file-tab:hover {
            background-color: #e9ecef;
        }

        .file-tab.active {
            background-color: white;
            border-bottom: 1px solid white;
            z-index: 1;
        }

        .file-tab span {
            flex: 1;
            font-size: 14px;
        }

        .delete-btn {
            margin-left: 8px;
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            font-size: 16px;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 3px;
        }

        .delete-btn:hover {
            background-color: #dc3545;
            color: white;
        }

        .new-file-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }

        .new-file-btn:hover {
            background-color: #218838;
        }

        .editor {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .editor textarea {
            flex: 1;
            border: none;
            outline: none;
            padding: 16px;
            font-family: 'Courier New', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            background-color: white;
            color: #333;
            tab-size: 4;
        }

        .editor-toolbar {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 8px 16px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 12px;
            color: #6c757d;
        }

        .run-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .run-btn:hover {
            background-color: #218838;
        }

        .format-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .format-btn:hover {
            background-color: #5a6268;
        }

        .theme-btn {
            background-color: #6f42c1;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .theme-btn:hover {
            background-color: #5a32a3;
        }

        /* Dark theme styles */
        .dark-theme {
            background-color: #1e1e1e;
            color: #d4d4d4;
        }

        .dark-theme .editor-panel {
            background-color: #252526;
            border-right-color: #3e3e42;
        }

        .dark-theme .file-tabs {
            background-color: #2d2d30;
            border-bottom-color: #3e3e42;
        }

        .dark-theme .file-tab {
            background-color: #2d2d30;
            border-color: #3e3e42;
            color: #cccccc;
        }

        .dark-theme .file-tab:hover {
            background-color: #37373d;
        }

        .dark-theme .file-tab.active {
            background-color: #1e1e1e;
            border-bottom-color: #1e1e1e;
        }

        .dark-theme .editor-toolbar {
            background-color: #252526;
            border-bottom-color: #3e3e42;
            color: #cccccc;
        }

        .dark-theme .editor textarea {
            background-color: #1e1e1e;
            color: #d4d4d4;
        }

        .dark-theme .preview-header {
            background-color: #2d2d30;
        }

        .preview-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: white;
        }

        .preview-header {
            background-color: #343a40;
            color: white;
            padding: 12px 16px;
            margin: 0;
        }

        .preview-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }

        .preview-iframe {
            flex: 1;
            border: none;
            background-color: white;
        }

        /* Scrollbar styling for webkit browsers */
        .editor textarea::-webkit-scrollbar {
            width: 8px;
        }

        .editor textarea::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .editor textarea::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .editor textarea::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .app {
                flex-direction: column;
            }
            
            .editor-panel {
                border-right: none;
                border-bottom: 2px solid #e0e0e0;
            }
            
            .file-tabs {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="app">
        <div class="editor-panel">
            <div class="file-tabs" id="fileTabs">
                <!-- File tabs will be generated here -->
            </div>
            
            <div class="editor">
                <div class="editor-toolbar">
                    <span id="fileInfo">Lines: 1 | Characters: 0</span>
                    <button class="run-btn" onclick="updatePreview()">▶ Run</button>
                    <button class="format-btn" onclick="debugPreview()">🔍 Debug</button>
                    <button class="format-btn" onclick="formatCode()">Format</button>
                    <button class="theme-btn" onclick="toggleTheme()">🌙 Dark</button>
                    <button class="format-btn" onclick="saveProject()">💾 Save</button>
                    <button class="format-btn" onclick="loadProject()">📁 Load</button>
                </div>
                <textarea id="codeEditor" placeholder="Edit your code here..."></textarea>
            </div>
        </div>
        
        <div class="preview-panel">
            <div class="preview-header">
                <h3>Live Preview</h3>
            </div>
            <iframe id="previewFrame" class="preview-iframe"></iframe>
        </div>
    </div>

    <script>
        // Initial files with sample content
        const files = {
            'index.html': `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Project</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Hello World!</h1>
        <p>Welcome to your HTML, CSS, and JavaScript playground!</p>
        <button id="myButton">Click me!</button>
        <div id="output"></div>
    </div>
    <script src="script.js"></script>
</body>
</html>`,
            'style.css': `body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f0f0f0;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 20px;
}

p {
    color: #666;
    text-align: center;
    font-size: 18px;
    margin-bottom: 30px;
}

button {
    display: block;
    margin: 20px auto;
    padding: 12px 24px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #0056b3;
}

#output {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    text-align: center;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}`,
            'script.js': `document.addEventListener('DOMContentLoaded', function() {
    console.log('JavaScript loaded successfully!');

    const button = document.getElementById('myButton');
    const output = document.getElementById('output');
    let clickCount = 0;

    if (button && output) {
        button.addEventListener('click', function() {
            clickCount++;

            // Update button text
            this.textContent = \`Clicked \${clickCount} time\${clickCount === 1 ? '' : 's'}!\`;

            // Update output area
            output.innerHTML = \`
                <div>
                    <h3>🎉 Button clicked!</h3>
                    <p>Click count: <strong>\${clickCount}</strong></p>
                    <p>Time: \${new Date().toLocaleTimeString()}</p>
                </div>
            \`;

            // Change container background color
            const container = document.querySelector('.container');
            if (container) {
                const colors = ['#e8f4fd', '#f0f8e8', '#fdf4e8', '#f8e8fd', '#e8f8f4'];
                container.style.backgroundColor = colors[clickCount % colors.length];
            }
        });

        // Initial message
        output.innerHTML = '<p>👆 Click the button above to see JavaScript in action!</p>';
    } else {
        console.error('Button or output element not found!');
    }
});`
        };

        let activeFile = 'index.html';
        const codeEditor = document.getElementById('codeEditor');
        const previewFrame = document.getElementById('previewFrame');
        const fileTabs = document.getElementById('fileTabs');

        // Initialize the editor
        function init() {
            renderFileTabs();
            loadFile(activeFile);
            updatePreview();
        }

        // Render file tabs
        function renderFileTabs() {
            fileTabs.innerHTML = '';
            
            Object.keys(files).forEach(fileName => {
                const tab = document.createElement('div');
                tab.className = `file-tab ${activeFile === fileName ? 'active' : ''}`;
                tab.innerHTML = `
                    <span>${fileName}</span>
                    ${Object.keys(files).length > 1 ? `<button class="delete-btn" onclick="deleteFile('${fileName}', event)">×</button>` : ''}
                `;
                tab.onclick = () => switchFile(fileName);
                fileTabs.appendChild(tab);
            });

            // Add new file button
            const newFileBtn = document.createElement('button');
            newFileBtn.className = 'new-file-btn';
            newFileBtn.textContent = '+';
            newFileBtn.onclick = createNewFile;
            fileTabs.appendChild(newFileBtn);
        }

        // Switch to a different file
        function switchFile(fileName) {
            // Save current file content
            files[activeFile] = codeEditor.value;
            
            // Switch to new file
            activeFile = fileName;
            loadFile(fileName);
            renderFileTabs();
            updatePreview();
        }

        // Load file content into editor
        function loadFile(fileName) {
            codeEditor.value = files[fileName] || '';
            codeEditor.placeholder = `Edit ${fileName}...`;
        }

        // Update preview
        function updatePreview() {
            const htmlContent = files['index.html'] || '';
            const cssContent = files['style.css'] || '';
            const jsContent = files['script.js'] || '';

            let fullContent = htmlContent;

            // Replace CSS link with embedded styles
            if (fullContent.includes('<link rel="stylesheet" href="style.css">')) {
                fullContent = fullContent.replace('<link rel="stylesheet" href="style.css">', `<style>${cssContent}</style>`);
            } else if (cssContent && !fullContent.includes('<style>')) {
                // If no CSS link found, add styles to head
                fullContent = fullContent.replace('</head>', `<style>${cssContent}</style>\n</head>`);
            }

            // Replace JS script with embedded script
            if (fullContent.includes('<script src="script.js"></script>')) {
                fullContent = fullContent.replace('<script src="script.js"></script>', `<script>${jsContent}</script>`);
            } else if (jsContent && !fullContent.includes('<script>')) {
                // If no JS script found, add script before closing body
                fullContent = fullContent.replace('</body>', `<script>${jsContent}</script>\n</body>`);
            }

            // If there's no HTML structure, create a basic one
            if (!fullContent.includes('<html>') && (cssContent || jsContent)) {
                fullContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview</title>
    ${cssContent ? `<style>${cssContent}</style>` : ''}
</head>
<body>
    ${fullContent}
    ${jsContent ? `<script>${jsContent}</script>` : ''}
</body>
</html>`;
            }

            previewFrame.srcdoc = fullContent;
        }

        // Create new file
        function createNewFile() {
            const fileName = prompt('Enter file name (with extension):');
            if (fileName && !files[fileName]) {
                files[fileName] = '';
                switchFile(fileName);
            }
        }

        // Delete file
        function deleteFile(fileName, event) {
            event.stopPropagation();
            if (Object.keys(files).length > 1) {
                delete files[fileName];
                
                if (activeFile === fileName) {
                    activeFile = Object.keys(files)[0];
                    loadFile(activeFile);
                }
                
                renderFileTabs();
                updatePreview();
            }
        }

        // Auto-save and update preview on code change
        codeEditor.addEventListener('input', function() {
            files[activeFile] = this.value;
            updateFileInfo();
            // Add a small delay to prevent too frequent updates
            clearTimeout(window.previewUpdateTimeout);
            window.previewUpdateTimeout = setTimeout(updatePreview, 300);
        });

        // Update file info (lines and characters)
        function updateFileInfo() {
            const content = codeEditor.value;
            const lines = content.split('\n').length;
            const characters = content.length;
            document.getElementById('fileInfo').textContent = `Lines: ${lines} | Characters: ${characters}`;
        }

        // Basic code formatting
        function formatCode() {
            const content = codeEditor.value;
            let formatted = content;

            if (activeFile.endsWith('.html')) {
                // Basic HTML formatting
                formatted = formatHTML(content);
            } else if (activeFile.endsWith('.css')) {
                // Basic CSS formatting
                formatted = formatCSS(content);
            } else if (activeFile.endsWith('.js')) {
                // Basic JS formatting
                formatted = formatJS(content);
            }

            codeEditor.value = formatted;
            files[activeFile] = formatted;
            updatePreview();
        }

        // Simple HTML formatter
        function formatHTML(html) {
            return html
                .replace(/></g, '>\n<')
                .replace(/^\s+|\s+$/gm, '')
                .split('\n')
                .map((line, index, array) => {
                    const trimmed = line.trim();
                    if (!trimmed) return '';

                    let indent = 0;
                    for (let i = 0; i < index; i++) {
                        const prevLine = array[i].trim();
                        if (prevLine.match(/<[^\/][^>]*[^\/]>$/)) indent++;
                        if (prevLine.match(/<\/[^>]+>$/)) indent--;
                    }

                    return '  '.repeat(Math.max(0, indent)) + trimmed;
                })
                .join('\n');
        }

        // Simple CSS formatter
        function formatCSS(css) {
            return css
                .replace(/\s*{\s*/g, ' {\n  ')
                .replace(/;\s*/g, ';\n  ')
                .replace(/\s*}\s*/g, '\n}\n\n')
                .replace(/,\s*/g, ',\n')
                .trim();
        }

        // Simple JS formatter
        function formatJS(js) {
            return js
                .replace(/\s*{\s*/g, ' {\n  ')
                .replace(/;\s*/g, ';\n  ')
                .replace(/\s*}\s*/g, '\n}\n\n')
                .trim();
        }

        // Add keyboard shortcuts
        codeEditor.addEventListener('keydown', function(e) {
            // Tab key for indentation
            if (e.key === 'Tab') {
                e.preventDefault();
                const start = this.selectionStart;
                const end = this.selectionEnd;

                this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                this.selectionStart = this.selectionEnd = start + 4;
            }

            // Ctrl+S to save (update preview)
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                updatePreview();
            }

            // Ctrl+/ for comment toggle
            if (e.ctrlKey && e.key === '/') {
                e.preventDefault();
                toggleComment();
            }
        });

        // Toggle comment functionality
        function toggleComment() {
            const start = codeEditor.selectionStart;
            const end = codeEditor.selectionEnd;
            const content = codeEditor.value;
            const lines = content.split('\n');

            // Find which lines are selected
            let startLine = content.substring(0, start).split('\n').length - 1;
            let endLine = content.substring(0, end).split('\n').length - 1;

            // Toggle comments for selected lines
            for (let i = startLine; i <= endLine; i++) {
                if (activeFile.endsWith('.html')) {
                    if (lines[i].trim().startsWith('<!--')) {
                        lines[i] = lines[i].replace(/<!--\s*/, '').replace(/\s*-->/, '');
                    } else {
                        lines[i] = '<!-- ' + lines[i] + ' -->';
                    }
                } else if (activeFile.endsWith('.css')) {
                    if (lines[i].trim().startsWith('/*')) {
                        lines[i] = lines[i].replace(/\/\*\s*/, '').replace(/\s*\*\//, '');
                    } else {
                        lines[i] = '/* ' + lines[i] + ' */';
                    }
                } else if (activeFile.endsWith('.js')) {
                    if (lines[i].trim().startsWith('//')) {
                        lines[i] = lines[i].replace(/\/\/\s*/, '');
                    } else {
                        lines[i] = '// ' + lines[i];
                    }
                }
            }

            codeEditor.value = lines.join('\n');
            files[activeFile] = codeEditor.value;
            updatePreview();
        }

        // Theme toggle functionality
        let isDarkTheme = false;

        function toggleTheme() {
            isDarkTheme = !isDarkTheme;
            const body = document.body;
            const themeBtn = document.querySelector('.theme-btn');

            if (isDarkTheme) {
                body.classList.add('dark-theme');
                themeBtn.textContent = '☀️ Light';
            } else {
                body.classList.remove('dark-theme');
                themeBtn.textContent = '🌙 Dark';
            }

            // Save theme preference
            localStorage.setItem('codeEditorTheme', isDarkTheme ? 'dark' : 'light');
        }

        // Load saved theme
        function loadTheme() {
            const savedTheme = localStorage.getItem('codeEditorTheme');
            if (savedTheme === 'dark') {
                toggleTheme();
            }
        }

        // Save project to localStorage
        function saveProject() {
            const projectName = prompt('Enter project name:', 'MyProject');
            if (projectName) {
                const projectData = {
                    files: files,
                    activeFile: activeFile,
                    timestamp: new Date().toISOString()
                };
                localStorage.setItem(`codeEditor_project_${projectName}`, JSON.stringify(projectData));
                alert(`Project "${projectName}" saved successfully!`);
            }
        }

        // Load project from localStorage
        function loadProject() {
            const projectName = prompt('Enter project name to load:');
            if (projectName) {
                const savedProject = localStorage.getItem(`codeEditor_project_${projectName}`);
                if (savedProject) {
                    const projectData = JSON.parse(savedProject);
                    Object.assign(files, projectData.files);
                    activeFile = projectData.activeFile || Object.keys(files)[0];
                    renderFileTabs();
                    loadFile(activeFile);
                    updatePreview();
                    updateFileInfo();
                    alert(`Project "${projectName}" loaded successfully!`);
                } else {
                    alert(`Project "${projectName}" not found!`);
                }
            }
        }

        // Auto-save current work
        function autoSave() {
            const autoSaveData = {
                files: files,
                activeFile: activeFile,
                timestamp: new Date().toISOString()
            };
            localStorage.setItem('codeEditor_autosave', JSON.stringify(autoSaveData));
        }

        // Load auto-saved work
        function loadAutoSave() {
            const autoSaved = localStorage.getItem('codeEditor_autosave');
            if (autoSaved) {
                const autoSaveData = JSON.parse(autoSaved);
                // Only load if it's recent (within last hour)
                const saveTime = new Date(autoSaveData.timestamp);
                const now = new Date();
                const hoursDiff = (now - saveTime) / (1000 * 60 * 60);

                if (hoursDiff < 1 && confirm('Found recent auto-saved work. Would you like to restore it?')) {
                    Object.assign(files, autoSaveData.files);
                    activeFile = autoSaveData.activeFile || Object.keys(files)[0];
                    renderFileTabs();
                    loadFile(activeFile);
                    updatePreview();
                    updateFileInfo();
                }
            }
        }

        // Auto-save every 30 seconds
        setInterval(autoSave, 30000);

        // Debug function to help troubleshoot preview issues
        function debugPreview() {
            const htmlContent = files['index.html'] || '';
            const cssContent = files['style.css'] || '';
            const jsContent = files['script.js'] || '';

            console.log('=== DEBUG PREVIEW ===');
            console.log('HTML Content:', htmlContent);
            console.log('CSS Content:', cssContent);
            console.log('JS Content:', jsContent);

            let fullContent = htmlContent;

            // Show the transformation process
            console.log('Original HTML:', fullContent);

            if (fullContent.includes('<link rel="stylesheet" href="style.css">')) {
                fullContent = fullContent.replace('<link rel="stylesheet" href="style.css">', `<style>${cssContent}</style>`);
                console.log('After CSS replacement:', fullContent);
            }

            if (fullContent.includes('<script src="script.js"></script>')) {
                fullContent = fullContent.replace('<script src="script.js"></script>', `<script>${jsContent}</script>`);
                console.log('After JS replacement:', fullContent);
            }

            console.log('Final content sent to iframe:', fullContent);
            console.log('=== END DEBUG ===');

            alert('Debug info logged to console. Open browser dev tools (F12) to see details.');
        }

        // Initialize the application
        init();
        updateFileInfo();
        loadTheme();
        loadAutoSave();
    </script>
</body>
</html>
